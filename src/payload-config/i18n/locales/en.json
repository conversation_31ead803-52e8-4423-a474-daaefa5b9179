{"home": {"onGoingEvent": "Ongoing & Upcoming Events", "viewDetail": "View Details", "bookTicket": "Book Ticket", "outstandingPerformers": "Outstanding Performers", "outstandingPerformersDescription": "Experience unforgettable performances from talented artists, bringing unique sounds and captivating energy to our stage", "pastEvents": "Past Events", "upcomingEvents": "Upcoming", "nowShowingEvents": "Now Showing", "sponsorsAndPartners": "Sponsors & Partners", "shows": "Shows", "contact": "Contact", "noShows": "No Upcoming Shows"}, "common": {"loading": "Loading...", "tryAgain": "Try Again", "goHome": "Go Home", "back": "Back", "cancel": "Cancel", "lastName": "Last Name", "firstName": "First Name", "enterLastName": "Enter your last name", "enterFirstName": "Enter your first name", "enterPhoneNumber": "Enter your phone number", "enterEmail": "Enter your email"}, "event": {"chooseAttendingDate": "Choose Your Attending Date", "pleaseChooseAttendingDate": "Please select a date", "ticketInformation": "Ticket Information", "pleaseClickOnTheTicketToBuy": "Please click on the ticket below to purchase", "pleaseChooseAttendingDateToViewMapSelectingSeat": "Please select a date to view the seating map", "choseTickets": "Selected Tickets", "pleaseSelectTicket": "Please select a ticket", "total": "Total", "holdSeatAndPay": "Hold Seat & Pay", "introduction": "Introduction", "viewMore": "View More", "viewLess": "View Less", "termsAndConditions": "Terms & Policies", "faq": "Frequently Asked Questions", "upcomingSale": "Opening Soon", "areYouReady": "Are You Ready?", "opportunityMessage": "An opportunity you can't miss! Tickets will officially go on sale soon. Get ready to secure your ticket and experience the most anticipated event!", "seat": "<PERSON><PERSON>", "confirmOrderAndPayment": "Confirm Ticket Booking & Payment", "recipientInfo": "Recipient Information", "lastName": "Last Name", "firstName": "First Name", "phoneNumber": "Phone Number", "email": "Email", "enterLastName": "Enter your last name", "enterFirstName": "Enter your first name", "enterPhoneNumber": "Enter your phone number", "enterEmail": "Enter your email", "lastNameRequired": "Last name is required", "firstNameRequired": "First name is required", "phoneNumberRequired": "Phone number is required", "emailRequired": "Email is required", "emailInvalidFormat": "Invalid email format", "selectPaymentMethod": "Pay via QR or digital wallet", "orderSummary": "Order Summary", "ticketInfo": "Ticket Information", "enterPromoCode": "Enter Promo Code", "applyCode": "Apply Code", "promoCodeAppliedTo": "Promo code applies only to the following ticket types:", "promoCode": "Promo Code", "totalBeforeDiscount": "Total Before Discount", "totalAfterDiscount": "Total After Discount", "confirmAndPay": "Confirm & Pay", "confirmAndGoToPayment": "Confirm & Proceed to Payment", "close": "Close", "paymentTerms": "By clicking 'Confirm & Pay', you agree to our Terms of Service and Privacy Policy. Your ticket will be sent via email upon successful payment", "bankTransferTerms": "Your ticket will be sent via email after we confirm successful payment", "selectDateToAttend": "Select the date you want to attend", "termsAndPolicies": "Terms & Policies", "schedule": "Schedule", "bankTransferPayment": "Bank Transfer Payment", "qrCodeMethod": "Method 1: Open your bank app/wallet and scan the QR code", "manualTransferMethod": "Method 2: Transfer manually using the details", "scanQRToTransfer": "Scan this QR code to transfer", "downloadQR": "Download QR Image", "transferContent": "Transfer Description", "bank": "Bank", "beneficiary": "Beneficiary", "accountNumber": "Account Number", "amount": "Amount", "copy": "Copy", "copied": "<PERSON>pied", "transferNote": "Note: After successfully transferring, please click the button below to confirm your payment", "transactionCode": "Transaction Code", "enterTransactionCode": "Enter transaction code", "transactionCodeRequired": "Transaction code is required", "transactionImage": "Transaction Image", "clickToUpload": "Click to upload image", "dragAndDrop": "or drag and drop here", "imageFormat": "PNG, JPG, or JPEG (Max 5MB)", "confirmTransfer": "I confirm that I have successfully transferred the payment", "confirmRequest": "Confirm Your Request", "confirmMessage": "Thank you! We will verify and send details to your email within 24 hours", "qrError": "An error occurred while displaying the QR code. Please reload the page or manually enter the transfer details for payment", "booking": "Booking", "stageMap": "Stage Map", "selectedSeats": "Selected Seats", "enterTicketQuantity": "Please enter the ticket quantity", "quantityMustBeNumber": "Please enter a number", "quantityMustBePositive": "Quantity must be greater than 0", "quantityMustBeInteger": "Quantity must be an integer", "nextPagePayment": "Proceed to Payment (QR/Card)", "ticket": "Tickets", "availablePromotions": "Available Promotions", "off": "off", "currencySymbol": "₫", "requiresMinimumTickets": "Requires a minimum of {{count}} tickets", "noPromotionsAvailable": "No promotions available", "promotionNotMeetConditions": "Minimum conditions not met to apply promo code", "dateTime": "Date & time", "event": "Event", "selectTicket": "Select Tickets", "checkout": "Checkout", "notAvailable": "Not Available", "selected": "Selected", "stage": "Stage", "balcony": "Balcony", "ticketPrices": "Ticket Prices", "discountOnTotalOrderValue": "Apply on total order value", "discountOnPerOrderItem": "Apply on each ticket in the order", "guideToPayment": "After selecting your seats, please scroll down to reserve and proceed with payment.", "maxPromotions": "You can only apply a maximum of {{count}} promo codes", "bookNow": "Book Now"}, "seatSelection": {"booking": "Booking", "stageMap": "Stage Map", "selectDatePrompt": "Please select a date to view the seating map", "selectedTickets": "Selected Tickets", "selectTicketsPrompt": "Please select a ticket", "selectedSeats": "Selected Seats", "total": "Total", "holdAndPay": "Hold Seat & Pay", "cannotSelectSeat": "Cannot select seats together that way", "noEmptySeats": "Kindly select adjacent seats to avoid empty seats in between", "close": "Close", "proceedToPayment": "Proceed to Payment"}, "message": {"errorHoldingSeatAndPayment": "An error occurred while holding the seat and processing the payment", "operationFailed": "Operation failed", "errorOccurred": "An error occurred! Please try again", "promotionFailed": "Failed", "enterPromoCode": "Please enter a promo code", "promotionSuccess": "Success", "promotionApplied": "Promo code applied", "invalidPromoCode": "Invalid promo code", "errorHoldingSeat": "An error occurred while holding the seat and processing the payment", "success": "Success", "failed": "Failed", "submitting": "Submitting...", "submit": "Submit", "thankYouForYourSubmission": "Thank you! Your information has been submitted successfully. We will contact you soon.", "failedMessage": "An error occurred! Please try again", "requiredField": "This field is required", "invalidEmail": "Please enter a valid email address"}, "errorCode": {"SYS001": "An error occurred! Please try again", "PROMO001": "The discount code cannot be empty", "PROMO002": "Invalid discount code(s) {{promotionCode}}", "PROMO003": "The discount code {{promotionCode}} has reached its usage limit", "PROMO004": "The discount code cannot be used before the specified time", "PROMO005": "The discount code has expired", "PROMO006": "The minimum number of tickets required to apply the discount code has not been met", "PROMO007": "You have used all available uses for the discount code {{promotionCode}}", "PROMO008": "The minimum number of tickets required to apply the discount code {{promotionCode}} has not been met", "PROMO009": "Multiple discount codes cannot be applied to a single order", "PROMO010": "A maximum of {{maxAppliedPromotions}} discount codes can be applied to a single order", "PROMO011": "The discount code(s) {{promotionCodes}} are invalid", "EVT001": "The event cannot be empty", "EVT002": "The event does not exist", "EVT003": "The event is not open for sale", "EVT004": "The event has been closed for sales", "EVT005": "The event has been canceled", "EVT006": "The event participation date cannot be empty", "EVT007": "Invalid participation date for the event {{eventTitle}}", "EVT008": "The event ID cannot be empty", "EVT009": "The event schedule ID cannot be empty", "SEAT001": "The seat cannot be empty", "SEAT002": "The seat(s) {{seats}} is being held by someone else! Please select another seat", "SEAT003": "The seat(s) {{seats}} has already been booked. Please select another seat", "SEAT004": "The seat selection session has expired! Please reselect your seats and try again", "SEAT005": "Duplicate seat {{seat}} detected! Please check again", "TICK001": "The ticket type cannot be empty", "TICK002": "The ticket type name cannot be empty", "TICK003": "The number of tickets must be a positive integer", "TICK004": "The ticket type does not exist", "TICK005": "Tickets for {{ticketClass}} are sold out! Please select another ticket", "TICK006": "Only {{remaining}} tickets are available for {{ticketClass}}! Please adjust the quantity", "TICK007": "The ticket type does not exist for the event {{eventTitle}}", "TICK008": "Seats for ticket class {{ticketClass}} on the selected date are sold out! Please choose a different ticket class", "TICK009": "Only {{remaining}} seats are available for ticket class {{ticketClass}} on the selected date! Please adjust your selection", "TICK010": "The ticket price ID cannot be empty", "BOOK001": "Invalid booking type", "ORD001": "The order category cannot be empty", "ORD002": "The order does not exist", "ORD003": "The order has already been paid", "ORD004": "The order has expired", "ORD005": "Please select your seat and try again", "ORD006": "The adjustment amount is invalid", "ORD007": "The adjustment amount cannot be negative", "CHECKIN001": "Ticket not found", "CHECKIN002": "Seat is not assigned to ticket", "CHECKIN003": "Ticket already checked in", "CHECKIN004": "Check-in failed", "CHECKIN005": "Unauthorized - Invalid admin user", "CHECKIN006": "Check-in record not found", "CHECKIN007": "Admin ID and at least one ticket code are required", "CHECKIN008": "Admin not found", "CHECKIN009": "All ticket codes must belong to the same user", "CHECKIN010": "Email and ticket code are required", "CHECKIN011": "Invalid ticket code", "CHECKIN012": "Email does not match ticket records", "CHECKIN013": "Ticket code is required", "CHECKIN014": "Please choose event, it is required", "CUS001": "First name is required", "CUS002": "Last name is required", "CUS003": "Phone number is required", "CUS004": "Email is required", "UNAUTHORIZED": "Unauthorized"}, "error": {"title": "Oops! Something went wrong", "description": "Don't worry, we've recorded the error and will fix it soon. You can click retry or return to the homepage.", "tryingAgain": "Trying again", "tryingAgainDescription": "Attempting to recover from the error", "goingHome": "Going home", "goingHomeDescription": "Navigating to the home page"}, "ticket": {"getDirections": "Get Directions", "saveQRCode": "Save QR Code", "ticket": "Ticket", "readyToCheckIn": "Ready to Check In", "checkedIn": "Checked In", "booked": "Booked", "qrCodeNotAvailable": "QR code available for booked tickets only.", "termsAndConditions": "Terms & Conditions"}, "checkout": {"personalInfo": "Personal Information", "lastName": "Last Name", "firstName": "First Name", "phone": "Phone Number", "email": "Email", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "ticketInfo": "Ticket Information", "promoCode": "Promo Code", "enterPromoCode": "Enter promo code", "apply": "Apply", "subtotal": "Subtotal", "discount": "Discount", "total": "Total", "termsAgreement": "I agree to the Terms and Conditions and Privacy Policy. I understand that my ticket will be sent to my email after payment is confirmed.", "confirmAndPay": "Confirm and Pay", "disclaimer": "Your tickets will be sent to your email after successful payment. Please check your email inbox (and spam folder) for your ticket confirmation.", "goingHomeDescription": "Navigating to the home page", "loginFailed": "An error occurred during login", "loadEventFailed": "Failed to load events", "failedToCheckIn": "Failed to check in", "failedToLoadEvents": "Failed to load events", "failedToLoadHistory": "Failed to load check-in history", "bulkCheckInFailed": "Bulk check-in failed", "bulkMarkGivenFailed": "Bulk mark-as-given failed", "unexpectedError": "Unexpected error"}, "checkin": {"adminCheckIn": "Admin Check-In", "signInToContinue": "Sign in to continue", "email": "Email", "enterYourEmail": "Enter your email", "password": "Password", "enterYourPassword": "Enter your password", "signIn": "Sign In", "signingIn": "Signing in...", "pleaseEnterEmailAndPassword": "Please enter both email and password", "login": "<PERSON><PERSON>", "loadingEvents": "Loading events...", "pleaseLoginFirst": "Please login first", "pleaseSelectEventAndSchedule": "Please select an event and schedule", "noSchedulesAvailable": "No schedules available for this event", "selected": "Selected", "selectEvent": "Select Event", "confirm": "Confirm", "checkInHistory": "Check-In History", "searchByTicketCodeOrSeat": "Search by ticket code or seat...", "back": "Back", "loadingCheckInHistory": "Loading check-in history...", "noRecordsFound": "No records found", "delete": "Delete", "confirmDeleteRecord": "Are you sure you want to delete this record?", "recordDeletedSuccessfully": "Record deleted successfully", "failedToDeleteRecord": "Failed to delete record", "ticketUsed": "TICKET USED", "validTicket": "VALID TICKET", "ticketDetails": "TICKET DETAILS", "name": "Name:", "event": {"dateTBA": "Date: TBA"}, "date": "Date:", "emailLabel": "Email:", "phoneNumber": "Phone Number:", "ticketType": "Ticket Type:", "seat": "Seat:", "checkedInAt": "Checked in at:", "by": "By:", "checkInNow": "CHECK IN NOW", "checkingIn": "CHECKING IN...", "alreadyCheckedIn": "ALREADY CHECKED IN", "invalidTicketData": "Invalid ticket data", "ticketCheckedInSuccessfully": "Ticket checked in successfully", "failedToCheckIn": "Failed to check in", "enterTicketCodeOrSeat": "Enter ticket code/Seat", "validateTicket": "Validate Ticket", "validating": "Validating...", "viewHistory": "View All Check-In", "multipleTicketsFound": "Multiple Tickets Found", "multipleTicketsFoundLabel": "Multiple Tickets Found", "pleaseEnterTicketCode": "Please enter a ticket code", "ticketAlreadyCheckedIn": "This ticket has already been checked in", "ticketNotFound": "Ticket not found", "schedule": "Schedule:", "ticketPriceInfo": "Ticket Price Info:", "ticketPriceInfoLabel": "Ticket Price Info:", "checkedIn": "Checked in:", "used": "Used", "valid": "<PERSON><PERSON>", "success": "Success", "byUsher": "By Usher", "nav": {"qr": "Checkin via QR", "search": "Checkin via Search"}, "scan": {"title": "Scan QR Code", "instruction": "Position the code within the frame", "upload": "Upload QR", "history": "Checkin History", "loadingHistory": "Loading history...", "noRecentScans": "No recent checkins.", "ticket": "Ticket:", "attendee": "Attendee:", "time": "Time:", "error": {"canvasNotSupported": "<PERSON><PERSON> not supported", "noQrFound": "No QR code found.", "failedToLoadImage": "Failed to load image.", "processingImage": "Error processing image.", "failed": "Check-in failed", "network": "Network error"}, "success": "Checked In"}}, "customerCheckinTicket": {"ticketCheckIn": "Self Check-In", "enterYourEmail": "Enter your email", "enterYourTicketCode": "Enter your ticket code", "checkIn": "Check in", "checkingIn": "Checking in...", "alreadyCheckedIn": "Already checked in", "showTicket": "Show this screen to receive ticket!", "confirmedSuccessfully": "Confirmed successfully", "zone": "Zone:", "ticketCode": "Ticket Code:", "seat": "Seat:", "eventName": "Event Name:", "email": "Email:", "attendeeName": "Attendee Name:", "checkedInAt": "Checked in at:", "ticketGiven": "Ticket Given", "confirmValidTicket": "Confirm valid ticket and give ticket", "checkInAnotherTicket": "Check in another ticket", "sisterTickets": "Other Tickets in This Order", "noSisterTickets": "No sister tickets available", "bulkCheckIn": "Bulk Check-In", "bulkMarkGiven": "Allow checkin multiple tickets", "checkInSelected": "Check In Selected", "markSelectedAsGiven": "Confirm sister tickets as valid and give ticket", "cancelBulk": "Cancel Bulk", "noSelection": "No selection", "selectAtLeastOneSisterTicket": "Select at least one sister ticket", "selectAtLeastOneTicket": "Select at least one ticket", "bulkCheckInSuccess": "Bulk Check-in", "checkedInSelectedTickets": "Checked in selected tickets", "bulkMarkGivenSuccess": "Give Selected Tickets", "markedSelectedTicketsAsGiven": "Successful marked selected tickets as given", "confirmTicketGiven": "Confirm this ticket has been given by admin \"{{adminId}}\"?", "confirmed": "Ticket received!", "success": "Success", "selectAll": "Select All", "deselectAll": "Deselect All"}, "userprofile": {"userProfile": "User Profile", "title": "My Tickets", "all": "All", "success": "Success", "processing": "Processing", "cancelled": "Cancelled", "canceled": "Canceled", "booked": "Booked", "gifted": "Gifted", "pending_payment": "Pending Payment", "upcoming": "Upcoming", "finished": "Finished", "month04": "April", "ticketTitle": "Your Ticket", "statusSuccess": "Success", "eTicket": "E-ticket", "orderCode": "Order Code", "dateRange": "09:00, April 24, 2025 – 17:00, April 26, 2025", "location": "Saigon Exhibition and Convention Center (SECC), 799 <PERSON><PERSON><PERSON>, District 7, Ho Chi Minh City", "seat": "<PERSON><PERSON>", "ticketPrice": "Ticket Type", "statusPendingPayment": "Pending Payment", "statusHold": "On Hold", "statusCancelled": "Cancelled", "userLogin": "<PERSON><PERSON>", "accountSettings": {"infoHelper": "Providing accurate information will help support you during ticket purchases or when ticket verification is needed.", "fullName": "Full Name", "enterFullName": "Enter your full name", "phone": "Phone Number", "enterPhone": "Enter your phone number", "email": "Email", "enterEmail": "Enter your email", "dob": "Date of Birth", "enterDob": "Enter your date of birth", "gender": "Gender", "selectGender": "Select gender", "male": "Male", "female": "Female", "other": "Other", "submit": "Submit"}, "sidebar": {"accountOf": "Account of", "accountSettings": "Account <PERSON><PERSON>", "accountInfo": "Account Info", "purchasedTickets": "Purchased Tickets", "myEvents": "My Events", "becomeAmbassador": "Become Ambassador", "affiliateTerms": "Read Affiliate Terms & Conditions", "acceptTerms": "I accept the Affiliate Terms & Conditions", "confirmBecomeAmbassador": "Confirm", "cancel": "Cancel", "pleaseReadTerms": "Please read carefully the terms and conditions to become an Ambassador", "goToAffiliateDashboard": "Go to Affiliate Dashboard", "affiliateDashboard": "Affiliate Dashboard", "affiliateDashboardDesc": "View your affiliate performance, links, and rewards", "affiliatePendingTitle": "Affiliate Application Pending", "affiliatePendingDesc": "Your request to become an ambassador is under review. You will be notified once approved.", "affiliateSuccessTitle": "Congratulations! You are now an ambassador!", "affiliateSuccessDesc": "Your request to become an ambassador has been sent.", "affiliateErrorTitle": "Error", "affiliateErrorDesc": "Failed to send request. Please try again later.", "successToBecomeAmbassador": "You can now start earning rewards by sharing your affiliate link."}, "myEvents": "My Events", "noEvents": "You don't have any events", "noTickets": "No tickets of this category found", "upcomingTickets": "Upcoming Event Tickets", "pastTickets": "Past Event Tickets", "noUpcomingTickets": "No upcoming tickets found", "noPastTickets": "No past tickets found", "page": "Page {{current}} of {{total}}"}, "auth": {"signInSuccessfully": "Sign in successfully", "pleaseEnterEmail": "Please enter email", "forgotPasswordRequestSent": "Forgot password request sent", "checkYourEmail": "Please check your email for the reset link", "failedToSendRequest": "Failed to send request", "somethingWentWrong": "Something went wrong", "forgotPassword": "Forgot Password", "enterEmailForReset": "Enter your email to receive a password reset link", "email": "Email", "password": "Password", "enterYourEmail": "Enter your email", "sendingRequest": "Sending request...", "sendResetLink": "Send Reset Link", "backToLogin": "Back to Login", "missingToken": "Missing token", "invalidResetLink": "Invalid reset link", "pleaseEnterNewPassword": "Please enter your new password", "passwordsDoNotMatch": "Passwords do not match", "passwordResetSuccessful": "Password reset successful", "failedToResetPassword": "Failed to reset password", "resetPassword": "Reset Password", "enterNewPassword": "Enter your new password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "resettingPassword": "Resetting password...", "enterYourPassword": "Enter your password", "signIn": "Sign In", "signInToContinue": "Sign in to continue", "passwordMinLength": "Password must be at least {{length}} characters long", "passwordStrength": "Password must contain at least two of the following: uppercase letters, lowercase letters, numbers, or special characters", "firstTimeLoginGuideline": "If you've booked tickets before, you already have an account. Just use your email to reset your password, log in, and view your tickets and other details.", "firstTimeLoginLink": "First time logging in? Click here", "firstTimeLoginHeader": "Access Your Tickets", "firstTimeLoginEmailPlaceholder": "Enter the email you used to book tickets", "pleaseEnterEmailAndPassword": "Please enter both email and password", "signingIn": "Signing in...", "affiliateLoginLink": "Become our ambassador", "joinAmbassadorProgram": "Join Our Ambassador Program", "joinAmbassadorProgramDescription": "Earn rewards and exclusive benefits"}, "navbar": {"profile": "Profile", "logout": "Logout", "signInSignUp": "Sign In", "buyTicketNow": "Buy Ticket Now", "buyNow": "Buy Now"}, "paymentResult": {"processingPayment": "Processing Your Payment", "processingDescription": "Please wait while we confirm your payment...", "backToHome": "Back to Home", "paymentSuccessful": "Payment Successful! 🎉", "paymentFailed": "Payment Failed", "orderCanceled": "Order Canceled", "successDescription": "Your transaction has been completed successfully and your tickets are ready!", "canceledDescription": "Your order has been canceled because payment was not completed within the required timeframe.", "failedDescription": "There was an issue processing your payment. Please try again or contact support.", "orderDetails": "Order Details", "orderNumber": "Order Number", "dateTime": "Date & Time", "paymentMethod": "Payment Method", "onlinePayment": "Online Payment", "totalAmount": "Total Amount", "paymentNotCompleted": "Payment Not Completed", "failedReason": "Your payment could not be processed. This could be due to insufficient funds, incorrect payment details, or a temporary issue with the payment provider.", "canceledReason": "Your order was automatically canceled because the payment was not completed within the required timeframe. If this was unintentional, please place a new order and ensure payment is made to confirm your purchase.", "whatWouldYouLikeToDo": "What would you like to do?", "tryPaymentAgain": "Try the payment again with the same or different payment method", "checkCardDetails": "Check your card details and available balance", "contactSupport": "Contact customer support if the issue persists", "tryAgain": "Try Again", "ticketsSent": "🎫 Your tickets have been sent to your email address. You can also view them in your account dashboard.", "goToUpcomingEvents": "Go to upcoming events"}}